# AI编程助手提示工程规范

## 1. 系统通用规则

### 基础要求
1. **语言规范**：全程使用中文回答
2. **输出格式**：优先结构化输出（JSON/XML/函数调用/表格字段等）
3. **工作流程**：先计划、后执行
   - 禁止过早回答
   - 先复述需求与不确定点（通常≤3，重要时可适当增加），确认后再产出方案/补丁
   - 对于单指令、无歧义的微小修改，可跳过计划确认环节
4. **变更原则**：新增需求或修改bug时优先提供"最小变更补丁"而非整文件重写；必要时给出差异或替换片段位置
5. **验证机制**：产出必须包含"如何验证"的步骤（命令、预期输出/断言、通过标准）
6. **知识边界**：未知的API/文件/指令必须标注"未知"，给出最小需要补充的信息清单；信息不确定时明确说明"不确定/需要补充"
7. **组件化设计**：遵循模块化与单一职责，单文件代码≤400行，必要时按功能边界拆分
8. **方案对比**：对于代码生成/架构设计/复杂修复等关键任务，可生成多候选方案并基于约束满足/一致性/可验证性选择最佳

### MCP交互规则
在以下时机主动调用 mcp-feedback-enhanced：
- 方案或计划执行前
- 多文件/跨层变更或复杂改动前
- 需求/范围变化时
- 风险操作前

---

## 2. 场景化提示词模板

### 2.1 项目规划提示词
```
创建一个名为【应用名称】的【应用类型：网站/iOS App/微信小程序/浏览器插件】应用，这个应用的核心是解决【目标用户】在【用户的需求和问题】方面的问题。

请你设计这个应用的核心功能、拓展功能、业务逻辑，但是暂不实现具体功能。然后创建README.MD文件，需要包含功能设计、页面设计、目录结构、技术框架、UI设计规范、开发方案等，最后根据应用功能的重要性划分优先级、规划项目功能的开发顺序。

编写完成后，调用feedback mcp等我确认。
```

### 2.2 新功能实现提示词
```
实现{功能名称}功能：
- 核心需求：{3-5个关键需求点}
- 技术栈：{具体技术}
- 集成点：{与现有代码的接口}
- 请先提供架构设计，再逐步实现
```

### 2.3 需求复述提示词
```
请复述一遍我的需求，并详细说明你的实现方案，由我来确认你的理解是否准确，在我确认后，你再执行具体的实现方案。
```

---

## 3. 调试与修复提示词

### 3.1 综合调试提示词（适用于复杂问题）
```
【角色设定】
你是个资深的工程师，有20年的开发经验，请你完整阅读所有项目文件，了解整个项目，排查、假设出现问题的原因！我是个技术小白，请你用最简单直白的语言和我描述产生这个问题的原因。

【分析步骤】
1. 给出3种方案：请你针对问题，给我三种正确的解决方案，并详细说明每一种方案的优劣势，然后给出你最推荐的解决方案
2. 确定方案可行性：你确认这个方案能够妥善解决我们遇见的问题吗？这个方案是否完全适配我现在的项目？这个方案是否还有哪些细节我们没有考虑到？
3. 执行步骤（复杂bug时用）：针对解决方案「x」，请你用Markdown格式输出关于这个解决方案的执行步骤，以及技术细节和注意事项！
```

### 3.2 精确调试提示词（适用于明确错误）
```
我有一个{语言/框架}的{功能描述}，它应该{预期行为}，但现在{实际行为}。

【问题详情】
- 错误信息：{精确错误}
- 输入示例：{具体输入}
- 预期输出：{期望结果}

请逐行分析可能的问题原因。
```

---

## 4. 代码优化提示词

### 4.1 重构专用提示词
```
重构以下代码以提升{具体目标：性能/可读性/可维护性}：

【重构要求】
- 当前问题：{具体问题描述}
- 技术约束：{版本/依赖限制}
- 成功标准：{量化指标}

请提供重构方案并解释改进点。
```

---

## 5. 对话管理提示词

### 5.1 对话重启提示词
```
当前对话可能已经偏离主线或质量下降，请帮我生成一个完整的上下文总结md文件：

请按以下结构整合我们到目前为止的所有对话内容：

# 对话上下文总结

## 核心问题/目标
- 问题描述：{简述主要问题}
- 技术栈：{相关技术}
- 预期目标：{要达成的效果}

## 已尝试的方法
- ✅ 已完成：{列出已经成功的步骤}
- ❌ 已尝试但失败：{列出尝试过但无效的方法}
- 🔄 进行中：{当前正在处理的内容}

## 关键发现/线索
- 错误信息：{重要的错误提示}
- 有效信息：{有价值的发现}
- 约束条件：{技术限制或要求}

## 下一步计划
- 待验证：{需要确认的假设}
- 待尝试：{下一步可能的方向}
- 风险点：{需要注意的问题}

请确保信息完整准确，我将用这个总结开启新对话。
```

---

## 6. 使用说明

1. **选择合适的提示词**：根据具体场景选择对应的提示词模板
2. **填充占位符**：将模板中的{占位符}替换为具体内容
3. **遵循系统规则**：所有提示词都应遵循第1部分的系统通用规则
4. **迭代优化**：根据实际使用效果调整和完善提示词